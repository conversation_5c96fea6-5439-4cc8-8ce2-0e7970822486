# TruyenMoiGiay Server

A production-ready NestJS server with OpenAI proxy functionality, built with best practices and enterprise-grade features.

## Features

- 🚀 **OpenAI API Proxy** - Secure proxy for OpenAI chat completions with function calling support using official OpenAI SDK
- 🛡️ **Security** - Helmet, CORS, rate limiting, input validation
- 📝 **API Documentation** - Swagger/OpenAPI documentation
- 🔍 **Health Checks** - Comprehensive health monitoring endpoints
- 📊 **Structured Logging** - JSON/simple format logging with configurable levels
- ⚙️ **Configuration Management** - Environment-based configuration with validation
- 🔄 **Graceful Shutdown** - Proper application lifecycle management
- 📈 **Response Transformation** - Consistent API response format
- 🎯 **TypeScript Strict Mode** - Enhanced type safety
- 🧪 **Input Validation** - DTO-based request validation with class-validator

## Architecture

```
src/
├── common/           # Shared utilities, types, constants
│   ├── constants/    # Application constants
│   ├── filters/      # Exception filters
│   ├── interceptors/ # Request/response interceptors
│   ├── logger/       # Custom logging service
│   └── types/        # TypeScript type definitions
├── config/           # Configuration management
├── health/           # Health check endpoints
└── openai-proxy/     # OpenAI proxy module
    └── dto/          # Data transfer objects
```

## Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Copy environment configuration:
```bash
cp .env.example .env
```

4. Configure your environment variables in `.env`:
```bash
API_KEY=your_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
PORT=51732
NODE_ENV=development
```

### Running the Application

```bash
# Development mode with hot reload
npm run start:dev

# Production mode
npm run start:prod

# Build the application
npm run build
```

### API Documentation

Once the application is running, visit:
- **Swagger UI**: http://localhost:51732/api/docs (requires API key authentication)
- **Health Check**: http://localhost:51732/health
- **Liveness Probe**: http://localhost:51732/health/liveness
- **Readiness Probe**: http://localhost:51732/health/readiness

**Note:** Swagger documentation is protected by API key authentication. Use your API key via `X-API-Key` header or `Authorization: Bearer` token to access.

## Configuration

The application uses environment-based configuration with validation. See `.env.example` for all available options.

### Key Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `API_KEY` | API key for authentication (required) | - |
| `OPENAI_API_KEY` | OpenAI API key (required) | - |
| `PORT` | Server port | 51732 |
| `NODE_ENV` | Environment | development |
| `LOG_LEVEL` | Logging level | info |
| `THROTTLE_LIMIT` | Rate limit per TTL | 10 |

## API Usage

### OpenAI Proxy Endpoint

#### Basic Chat Completion
```bash
POST /proxy/openai
Content-Type: application/json
X-API-Key: your_api_key_here

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 150
}
```

#### Function Calling (Tools)
```bash
POST /proxy/openai
Content-Type: application/json
X-API-Key: your_api_key_here

{
  "model": "gpt-3.5-turbo",
  "messages": [
    { "role": "system", "content": "You are a story generator." },
    { "role": "user", "content": "Generate a fantasy story." }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "submit_story",
        "description": "Submits the generated fantasy story.",
        "parameters": {
          "type": "object",
          "properties": {
            "title": {
              "type": "string",
              "description": "The catchy, appealing title of the story in Vietnamese."
            },
            "content": {
              "type": "string",
              "description": "The full story text in Vietnamese, formatted using simple Markdown."
            }
          },
          "required": ["title", "content"]
        }
      }
    }
  ],
  "tool_choice": { "type": "function", "function": { "name": "submit_story" } }
}
```

## Development

### Code Quality

```bash
# Linting
npm run lint

# Formatting
npm run format
```

### Project Structure

- **Modular Architecture**: Each feature is organized in its own module
- **Separation of Concerns**: Clear separation between controllers, services, and DTOs
- **Type Safety**: Strict TypeScript configuration with comprehensive typing
- **Error Handling**: Global exception filter with structured error responses
- **Logging**: Structured logging with configurable levels and formats

## Production Deployment

### Environment Variables

Ensure all required environment variables are set:
- `API_KEY`: Your API key for authentication
- `OPENAI_API_KEY`: Your OpenAI API key
- `NODE_ENV=production`
- `LOG_LEVEL=warn` (recommended for production)
- `CORS_ORIGIN`: Specific origins for CORS

### Health Checks

The application provides health check endpoints for monitoring:
- `/health` - Comprehensive health check
- `/health/liveness` - Simple liveness probe
- `/health/readiness` - Readiness probe with dependency checks

### Security Features

- **API Key Authentication**: Secure endpoint protection with API keys
- **Helmet**: Security headers
- **CORS**: Configurable cross-origin resource sharing
- **Rate Limiting**: Configurable request throttling
- **Input Validation**: DTO-based request validation
- **Error Handling**: Secure error responses without sensitive data leakage

For detailed authentication information, see:
- [API Key Authentication Guide](docs/API_KEY_AUTHENTICATION.md)
- [Swagger Protection Guide](docs/SWAGGER_PROTECTION.md)

## License

This project is licensed under the MIT License.
