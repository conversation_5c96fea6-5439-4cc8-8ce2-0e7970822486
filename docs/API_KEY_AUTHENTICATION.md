# API Key Authentication

This document explains how to use the API key authentication system implemented in the TruyenMoiGiay server.

## Overview

The API key guard protects endpoints from unauthorized access by requiring a valid API key in the request headers. This ensures that only authorized clients can access the OpenAI proxy endpoints.

## Configuration

### Environment Variables

Add the following environment variable to your `.env` file:

```bash
API_KEY=your_secure_api_key_here
```

**Important**: Use a strong, unique API key in production. The API key should be:
- At least 32 characters long
- Include a mix of letters, numbers, and special characters
- Be kept secret and not shared publicly

### Example Configuration

```bash
# Development
API_KEY=dev-api-key-change-in-production

# Production
API_KEY=prod_sk_1234567890abcdef_secure_key_here
```

## Usage

### Making Authenticated Requests

You can provide the API key in two ways:

#### Option 1: X-API-Key Header (Recommended)

```bash
curl -X POST http://localhost:51732/proxy/openai \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key_here" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

#### Option 2: Authorization Bearer Token

```bash
curl -X POST http://localhost:51732/proxy/openai \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_key_here" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

### JavaScript/TypeScript Example

```typescript
const response = await fetch('http://localhost:51732/proxy/openai', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your_api_key_here'
  },
  body: JSON.stringify({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: 'Hello!' }]
  })
});

const data = await response.json();
```

## Error Responses

### 401 Unauthorized - Missing API Key

```json
{
  "statusCode": 401,
  "message": "API key is required",
  "error": "Unauthorized"
}
```

### 401 Unauthorized - Invalid API Key

```json
{
  "statusCode": 401,
  "message": "Invalid API key",
  "error": "Unauthorized"
}
```

## Swagger Documentation

The API documentation at `http://localhost:51732/api/docs` includes authentication options:

1. **X-API-Key**: Use the "Authorize" button and enter your API key in the X-API-Key field
2. **Bearer Token**: Use the "Authorize" button and enter your API key as a Bearer token

## Security Considerations

1. **Keep API Keys Secret**: Never commit API keys to version control
2. **Use Environment Variables**: Store API keys in environment variables or secure configuration management
3. **Rotate Keys Regularly**: Change API keys periodically for better security
4. **Use HTTPS**: Always use HTTPS in production to protect API keys in transit
5. **Monitor Usage**: Log and monitor API key usage for suspicious activity

## Protected Endpoints

Currently, the following endpoints require API key authentication:

- `POST /proxy/openai` - OpenAI chat completions proxy
- `GET /api/docs` - Swagger documentation (when using api-key protection method)

## Implementation Details

The API key guard is implemented in `src/common/guards/api-key.guard.ts` and:

- Checks for API keys in both `X-API-Key` header and `Authorization: Bearer` header
- Compares the provided key against the configured `API_KEY` environment variable
- Logs authentication attempts for security monitoring
- Returns appropriate error messages for missing or invalid keys

The guard is applied at the controller level and works in conjunction with the existing throttling guard to provide comprehensive protection.
