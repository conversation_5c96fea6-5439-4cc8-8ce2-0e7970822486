# Swagger Documentation Protection

This document explains how to configure and use the Swagger documentation protection system in the TruyenMoiGiay server.

## Overview

The Swagger documentation at `/api/docs` is protected by authentication to prevent unauthorized access to your API documentation. This is especially important in production environments where you want to keep your API structure private.

## Protection Methods

The system supports multiple protection methods:

### 1. API Key Protection (Default)

Uses the same API key system as your main API endpoints.

**Configuration:**
```bash
SWAGGER_PROTECTION_ENABLED=true
SWAGGER_PROTECTION_METHOD=api-key
```

**Access Methods:**
- **X-API-Key Header:** `X-API-Key: your_api_key_here`
- **Authorization Bearer:** `Authorization: Bearer your_api_key_here`

### 2. Basic Authentication

Traditional username/password protection with HTTP Basic Auth.

**Configuration:**
```bash
SWAGGER_PROTECTION_ENABLED=true
SWAGGER_PROTECTION_METHOD=basic
SWAGGER_USERNAME=admin
SWAGGER_PASSWORD=secure_password
```

**Access Method:**
- **Basic Auth:** <PERSON><PERSON><PERSON> will prompt for username/password
- **Header:** `Authorization: Basic base64(username:password)`

### 3. Disabled Protection

Completely disable protection (not recommended for production).

**Configuration:**
```bash
SWAGGER_PROTECTION_ENABLED=false
# OR
SWAGGER_PROTECTION_METHOD=disabled
```

## Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SWAGGER_PROTECTION_ENABLED` | Enable/disable protection | `true` | No |
| `SWAGGER_PROTECTION_METHOD` | Protection method | `api-key` | No |
| `SWAGGER_USERNAME` | Username for basic auth | - | Only for basic auth |
| `SWAGGER_PASSWORD` | Password for basic auth | - | Only for basic auth |

### Valid Protection Methods

- `api-key` - Use API key authentication (same as main API)
- `basic` - Use HTTP Basic Authentication
- `disabled` - No protection (not recommended for production)

## Usage Examples

### API Key Protection

```bash
# Access with X-API-Key header
curl -H "X-API-Key: your_api_key_here" http://localhost:51732/api/docs

# Access with Bearer token
curl -H "Authorization: Bearer your_api_key_here" http://localhost:51732/api/docs
```

### Basic Authentication

```bash
# Access with basic auth
curl -u admin:secure_password http://localhost:51732/api/docs

# Access with Authorization header
curl -H "Authorization: Basic YWRtaW46c2VjdXJlX3Bhc3N3b3Jk" http://localhost:51732/api/docs
```

### Browser Access

When accessing via browser:

1. **API Key Method:** You'll need to manually add the API key header using browser dev tools or a browser extension
2. **Basic Auth Method:** Browser will automatically prompt for username and password

## Error Responses

### 401 Unauthorized - API Key Method

```json
{
  "statusCode": 401,
  "message": "Valid API key required to access Swagger documentation. Provide via X-API-Key header or Authorization: Bearer token.",
  "error": "Unauthorized"
}
```

### 401 Unauthorized - Basic Auth Method

```json
{
  "statusCode": 401,
  "message": "Authentication required to access Swagger documentation",
  "error": "Unauthorized"
}
```

Response includes `WWW-Authenticate: Basic realm="Swagger Documentation"` header.

## Environment-Specific Configurations

### Development

```bash
# .env.development
SWAGGER_PROTECTION_ENABLED=true
SWAGGER_PROTECTION_METHOD=api-key
```

### Production

```bash
# .env.production
SWAGGER_PROTECTION_ENABLED=true
SWAGGER_PROTECTION_METHOD=basic
SWAGGER_USERNAME=admin
SWAGGER_PASSWORD=very_secure_production_password
```

## Security Considerations

1. **Use Strong Credentials:** For basic auth, use strong usernames and passwords
2. **HTTPS Only:** Always use HTTPS in production to protect credentials in transit
3. **Environment Variables:** Never commit credentials to version control
4. **Regular Rotation:** Change passwords and API keys regularly
5. **Access Logging:** Monitor access to Swagger documentation
6. **IP Restrictions:** Consider additional IP-based restrictions for production

## Implementation Details

### Middleware

The protection is implemented using `SwaggerAuthMiddleware` which:

- Intercepts all requests to `/api/docs*` routes
- Validates credentials based on the configured method
- Logs authentication attempts for security monitoring
- Returns appropriate error responses for unauthorized access

### Route Protection

The middleware is applied to all Swagger-related routes:
- `/api/docs` - Main Swagger UI page
- `/api/docs/*` - All Swagger assets (CSS, JS, etc.)

### Configuration Validation

The system validates configuration at startup:
- Ensures required credentials are provided for the selected method
- Falls back to API key method if configuration is invalid
- Logs configuration errors for debugging

## Troubleshooting

### Common Issues

1. **"API key not configured"**
   - Ensure `API_KEY` environment variable is set
   - Check that the API key matches between client and server

2. **"Basic auth credentials not configured"**
   - Set both `SWAGGER_USERNAME` and `SWAGGER_PASSWORD`
   - Ensure credentials are not empty

3. **Still accessible without authentication**
   - Check `SWAGGER_PROTECTION_ENABLED=true`
   - Verify `SWAGGER_PROTECTION_METHOD` is not set to `disabled`
   - Restart the application after configuration changes

### Debug Logging

Enable debug logging to troubleshoot authentication issues:

```bash
LOG_LEVEL=debug
```

This will log authentication attempts and configuration details.

## Migration Guide

### From Unprotected to Protected

1. Add environment variables to your `.env` file
2. Choose protection method (`api-key` recommended)
3. Restart the application
4. Update any automated tools or scripts that access Swagger

### Changing Protection Methods

1. Update `SWAGGER_PROTECTION_METHOD` in environment
2. Add/remove required credentials (username/password for basic auth)
3. Restart the application
4. Update client access methods accordingly
