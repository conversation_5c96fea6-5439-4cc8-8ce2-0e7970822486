# Application Configuration
NODE_ENV=development
PORT=51732

# API Key Configuration
API_KEY=your_api_key_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# CORS Configuration
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# Rate Limiting Configuration
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Swagger Documentation Protection
SWAGGER_PROTECTION_ENABLED=true
SWAGGER_PROTECTION_METHOD=api-key
# SWAGGER_USERNAME=admin
# SWAGGER_PASSWORD=secure_password
