import { Injectable, LoggerService, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../interfaces';

@Injectable()
export class CustomLoggerService implements LoggerService, Logger {
  private readonly currentLogLevel: string;
  private readonly logFormat: string;

  constructor(private readonly configService: ConfigService) {
    this.currentLogLevel =
      this.configService.get<string>('logging.level') ?? 'info';
    this.logFormat = this.configService.get<string>('logging.format') ?? 'json';
  }

  private shouldLog(level: LogLevel): boolean {
    const levelMap: Record<string, number> = {
      error: 0,
      warn: 1,
      log: 2, // 'info' maps to 'log' in NestJS
      info: 2, // alias for log
      debug: 3,
      verbose: 4,
    };

    const currentLevelIndex =
      levelMap[this.currentLogLevel] ?? levelMap['log']!;
    const messageLevelIndex = levelMap[level] ?? levelMap['log']!;

    return messageLevelIndex <= currentLevelIndex;
  }

  private formatMessage(
    level: LogLevel,
    message: unknown,
    context?: string,
    trace?: string,
  ): string {
    const timestamp = new Date().toISOString();
    const pid = process.pid;

    if (this.logFormat === 'json') {
      const logObject = {
        timestamp,
        level,
        context: context ?? 'Application',
        message:
          typeof message === 'object'
            ? JSON.stringify(message)
            : String(message),
        pid,
        ...(trace && { trace }),
      };
      return JSON.stringify(logObject);
    } else {
      // Simple format
      const contextStr = context ? `[${context}] ` : '';
      return `${timestamp} [${level.toUpperCase()}] ${contextStr}${String(message)}${trace ? `\n${trace}` : ''}`;
    }
  }

  log(message: unknown, context?: string): void {
    if (this.shouldLog('log')) {
      // eslint-disable-next-line no-console
      console.log(this.formatMessage('log', message, context));
    }
  }

  error(message: unknown, trace?: string, context?: string): void {
    if (this.shouldLog('error')) {
      // eslint-disable-next-line no-console
      console.error(this.formatMessage('error', message, context, trace));
    }
  }

  warn(message: unknown, context?: string): void {
    if (this.shouldLog('warn')) {
      // eslint-disable-next-line no-console
      console.warn(this.formatMessage('warn', message, context));
    }
  }

  debug(message: unknown, context?: string): void {
    if (this.shouldLog('debug')) {
      // eslint-disable-next-line no-console
      console.debug(this.formatMessage('debug', message, context));
    }
  }

  verbose(message: unknown, context?: string): void {
    if (this.shouldLog('verbose')) {
      // eslint-disable-next-line no-console
      console.log(this.formatMessage('verbose', message, context));
    }
  }
}
