export interface AppConfig {
  port: number;
  nodeEnv: string;
  apiKey: string;
  openai: OpenAiConfig;
  cors: CorsConfig;
  throttle: ThrottleConfig;
  logging: LoggingConfig;
  swagger: SwaggerConfig;
}

export interface OpenAiConfig {
  apiKey: string;
}

export interface CorsConfig {
  origin: string;
  credentials: boolean;
}

export interface ThrottleConfig {
  ttl: number;
  limit: number;
}

export interface LoggingConfig {
  level: string;
  format: string;
}

export interface SwaggerConfig {
  protectionEnabled: boolean;
  protectionMethod: 'api-key' | 'basic' | 'disabled';
  username?: string | undefined;
  password?: string | undefined;
}
