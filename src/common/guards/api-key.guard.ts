import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { CustomLoggerService } from '../logger/logger.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLoggerService,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const apiKey = this.extractApiKeyFromHeader(request);

    if (!apiKey) {
      this.logger.warn('API key missing in request headers', 'ApiKeyGuard');
      throw new UnauthorizedException('API key is required');
    }

    const configuredApiKey = this.configService.get<string>('apiKey');

    if (!configuredApiKey) {
      this.logger.error(
        'API key not configured in application settings',
        undefined,
        'ApiKeyGuard',
      );
      throw new UnauthorizedException('API key validation failed');
    }

    if (apiKey !== configuredApiKey) {
      this.logger.warn('Invalid API key provided in request', 'ApiKeyGuard');
      throw new UnauthorizedException('Invalid API key');
    }

    this.logger.debug('API key validation successful', 'ApiKeyGuard');

    return true;
  }

  private extractApiKeyFromHeader(request: Request): string | undefined {
    // Check for X-API-Key header
    const xApiKey = request.headers['x-api-key'];
    if (xApiKey && typeof xApiKey === 'string') {
      return xApiKey;
    }

    // Check for Authorization header with Bearer token
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return undefined;
  }
}
