import { HttpException, HttpStatus } from '@nestjs/common';

export abstract class BaseException extends HttpException {
  public readonly timestamp: string;
  public readonly path: string | undefined;

  constructor(message: string, status: HttpStatus, path?: string) {
    super(message, status);
    this.timestamp = new Date().toISOString();
    this.path = path;
  }

  public toJSON(): Record<string, unknown> {
    return {
      statusCode: this.getStatus(),
      message: this.message,
      error: this.constructor.name,
      timestamp: this.timestamp,
      path: this.path,
    };
  }
}
