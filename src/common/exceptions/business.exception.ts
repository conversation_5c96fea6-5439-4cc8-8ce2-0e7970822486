import { HttpStatus } from '@nestjs/common';
import { BaseException } from './base.exception';

export class ValidationException extends BaseException {
  constructor(message: string, path?: string) {
    super(message, HttpStatus.BAD_REQUEST, path);
  }
}

export class ConfigurationException extends BaseException {
  constructor(message: string, path?: string) {
    super(message, HttpStatus.INTERNAL_SERVER_ERROR, path);
  }
}

export class ExternalServiceException extends BaseException {
  constructor(
    message: string,
    public readonly serviceName: string,
    path?: string,
  ) {
    super(message, HttpStatus.BAD_GATEWAY, path);
  }

  public override toJSON(): Record<string, unknown> {
    return {
      ...super.toJSON(),
      serviceName: this.serviceName,
    };
  }
}

export class RateLimitException extends BaseException {
  constructor(message: string, path?: string) {
    super(message, HttpStatus.TOO_MANY_REQUESTS, path);
  }
}
