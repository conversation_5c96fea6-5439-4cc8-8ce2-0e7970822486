export const APP_CONSTANTS = {
  DEFAULT_PORT: 51732,
  DEFAULT_THROTTLE_TTL: 60,
  DEFAULT_THROTTLE_LIMIT: 10,
  DEFAULT_LOG_LEVEL: 'info',
  DEFAULT_LOG_FORMAT: 'json',
  DEFAULT_OPENAI_BASE_URL: 'https://api.openai.com/v1',
} as const;

export const HTTP_STATUS_MESSAGES = {
  200: 'Request successful',
  201: 'Resource created successfully',
  202: 'Request accepted',
  204: 'Request successful, no content',
  400: 'Bad request',
  401: 'Unauthorized',
  403: 'Forbidden',
  404: 'Not found',
  429: 'Too many requests',
  500: 'Internal server error',
  502: 'Bad gateway',
  503: 'Service unavailable',
} as const;

export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
  VERBOSE: 'verbose',
} as const;

export const NODE_ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
} as const;
