import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Request, Response, NextFunction } from 'express';
import { AppModule } from './app.module';
import { CustomLoggerService } from './common/logger/logger.service';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { ResponseTransformInterceptor } from './common/interceptors/response-transform.interceptor';
import { SwaggerAuthMiddleware } from './common/middleware';
import helmet from 'helmet';

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });

  const configService = app.get(ConfigService);
  const logger = app.get(CustomLoggerService);

  // Use custom logger
  app.useLogger(logger);

  // Global exception filter
  app.useGlobalFilters(new HttpExceptionFilter(logger));

  // Global interceptors
  app.useGlobalInterceptors(
    new LoggingInterceptor(logger),
    new ResponseTransformInterceptor(),
  );

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Security middleware
  app.use(helmet());

  // CORS configuration
  const corsConfig = configService.get<{
    origin: string;
    credentials: boolean;
  }>('cors');
  app.enableCors({
    origin: corsConfig?.origin ?? '*',
    credentials: corsConfig?.credentials ?? false,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
  });

  // Swagger documentation protection
  const swaggerAuthMiddleware = new SwaggerAuthMiddleware(
    configService,
    logger,
  );
  app.use('/api/docs*', (req: Request, res: Response, next: NextFunction) => {
    swaggerAuthMiddleware.use(req, res, next);
  });

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('TruyenMoiGiay Server API')
    .setDescription(
      'API documentation for TruyenMoiGiay server with OpenAI proxy functionality',
    )
    .setVersion('1.0')
    .addTag('OpenAI Proxy', 'OpenAI API proxy endpoints')
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'API key for authentication',
      },
      'X-API-Key',
    )
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'API Key',
        description: 'API key as Bearer token',
      },
      'Bearer',
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  const port = configService.get<number>('port') ?? 51732;

  // Enable graceful shutdown
  app.enableShutdownHooks();

  await app.listen(port);
  logger.log(
    `Application is running on: http://localhost:${port}`,
    'Bootstrap',
  );
  logger.log(
    `Swagger documentation available at: http://localhost:${port}/api/docs`,
    'Bootstrap',
  );

  // Graceful shutdown handling
  process.on('SIGTERM', () => {
    void (async (): Promise<void> => {
      logger.log('SIGTERM received, shutting down gracefully', 'Bootstrap');
      await app.close();
      process.exit(0);
    })();
  });

  process.on('SIGINT', () => {
    void (async (): Promise<void> => {
      logger.log('SIGINT received, shutting down gracefully', 'Bootstrap');
      await app.close();
      process.exit(0);
    })();
  });
}

bootstrap().catch((error: unknown) => {
  // eslint-disable-next-line no-console
  console.error('Failed to start application:', error);
  process.exit(1);
});
