import { Controller, Get } from '@nestjs/common';
import { Api<PERSON><PERSON>ation, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  DiskHealthIndicator,
  HealthCheck,
  HealthCheckService,
  MemoryHealthIndicator,
} from '@nestjs/terminus';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Health check',
    description: 'Check the overall health of the application',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is healthy',
  })
  @ApiResponse({
    status: 503,
    description: 'Application is unhealthy',
  })
  @HealthCheck()
  check(): Promise<unknown> {
    return this.health.check([
      // Check memory usage (should not exceed 150MB)
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      // Check RSS memory usage (should not exceed 150MB)
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      // Check disk storage (should have at least 250MB free)
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      () =>
        this.disk.checkStorage('storage', {
          path: '/',
          thresholdPercent: 0.9,
        }),
    ]);
  }

  @Get('liveness')
  @ApiOperation({
    summary: 'Liveness probe',
    description: 'Simple liveness check for Kubernetes/Docker',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is alive',
  })
  getLiveness(): { status: string; timestamp: string } {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
    };
  }
}
