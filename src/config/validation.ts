import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'production')
    .default('development'),
  PORT: Joi.number().default(51732),
  API_KEY: Joi.string().required(),
  OPENAI_API_KEY: Joi.string().required(),
  OPENAI_BASE_URL: Joi.string().uri().default('https://api.openai.com/v1'),
  CORS_ORIGIN: Joi.string().default('*'),
  CORS_CREDENTIALS: Joi.boolean().default(false),
  THROTTLE_TTL: Joi.number().default(60),
  THROTTLE_LIMIT: Joi.number().default(10),
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug', 'verbose')
    .default('info'),
  LOG_FORMAT: Joi.string().valid('json', 'simple').default('json'),
  SWAGGER_PROTECTION_ENABLED: Joi.boolean().default(true),
  SWAGGER_PROTECTION_METHOD: Joi.string()
    .valid('api-key', 'basic', 'disabled')
    .default('api-key'),
  SWAGGER_USERNAME: Joi.string().optional(),
  SWAGGER_PASSWORD: Joi.string().optional(),
});
