import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ToolCallFunctionDto {
  @ApiProperty({ description: 'The name of the function' })
  name!: string;

  @ApiProperty({ description: 'The arguments passed to the function' })
  arguments!: string;
}

export class ToolCallDto {
  @ApiProperty({ description: 'The ID of the tool call' })
  id!: string;

  @ApiProperty({
    description: 'The type of the tool call',
    example: 'function',
  })
  type!: string;

  @ApiProperty({ description: 'The function call details' })
  function!: ToolCallFunctionDto;
}

export class ResponseMessageDto {
  @ApiProperty({ description: 'The role of the message', example: 'assistant' })
  role!: string;

  @ApiPropertyOptional({ description: 'The content of the message' })
  content?: string | null;

  @ApiPropertyOptional({
    description: 'Tool calls made by the assistant',
    type: [ToolCallDto],
  })
  tool_calls?: ToolCallDto[];
}

export class ChoiceDto {
  @ApiProperty({ description: 'The index of the choice' })
  index!: number;

  @ApiProperty({
    description: 'The message generated by the model',
    type: ResponseMessageDto,
  })
  message!: ResponseMessageDto;

  @ApiProperty({
    description: 'The reason the model stopped generating tokens',
    example: 'stop',
  })
  finish_reason!: string;
}

export class UsageDto {
  @ApiProperty({ description: 'Number of tokens in the prompt' })
  prompt_tokens!: number;

  @ApiProperty({ description: 'Number of tokens in the completion' })
  completion_tokens!: number;

  @ApiProperty({ description: 'Total number of tokens used' })
  total_tokens!: number;
}

export class OpenAiResponseDto {
  @ApiProperty({ description: 'Unique identifier for the completion' })
  id!: string;

  @ApiProperty({ description: 'Object type', example: 'chat.completion' })
  object!: string;

  @ApiProperty({
    description: 'Unix timestamp of when the completion was created',
  })
  created!: number;

  @ApiProperty({ description: 'Model used for the completion' })
  model!: string;

  @ApiProperty({ description: 'List of completion choices', type: [ChoiceDto] })
  choices!: ChoiceDto[];

  @ApiProperty({ description: 'Usage statistics for the completion request' })
  usage!: UsageDto;
}
