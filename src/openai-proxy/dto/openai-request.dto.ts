import { IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ChatCompletionCreateParamsNonStreaming } from 'openai/resources/chat/completions';

export class OpenAiRequestDto
  implements ChatCompletionCreateParamsNonStreaming
{
  @ApiProperty({
    description: 'API key for OpenAI',
    example: 'your_openai_api_key_here',
  })
  @IsString()
  apiKey!: string;

  // All other properties are inherited from ChatCompletionCreateParamsNonStreaming
  model!: string;
  messages!: any[];
  frequency_penalty?: number | null;
  function_call?: any;
  functions?: any[];
  logit_bias?: Record<string, number> | null;
  logprobs?: boolean | null;
  max_tokens?: number | null;
  n?: number | null;
  presence_penalty?: number | null;
  response_format?: any;
  seed?: number | null;
  stop?: string | string[] | null;
  stream?: false | null;
  temperature?: number | null;
  tool_choice?: any;
  tools?: any[];
  top_logprobs?: number | null;
  top_p?: number | null;
  user?: string;
}
