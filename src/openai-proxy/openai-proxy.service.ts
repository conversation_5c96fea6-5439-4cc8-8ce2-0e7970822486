import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import type { ChatCompletionCreateParamsNonStreaming } from 'openai/resources/chat/completions';
import {
  ConfigurationException,
  ExternalServiceException,
} from '../common/exceptions';
import { CustomLoggerService } from '../common/logger/logger.service';
import { OpenAiConfig } from '../common/types';
import { OpenAiResponseDto } from './dto/openai-response.dto';
import { OpenAiProxyServiceInterface } from './interfaces/openai-proxy.interface';

@Injectable()
export class OpenAiProxyService implements OpenAiProxyServiceInterface {
  private readonly openai: OpenAI;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLoggerService,
  ) {
    const openaiConfig = this.configService.get<OpenAiConfig>('openai');

    if (!openaiConfig?.apiKey) {
      throw new ConfigurationException('OpenAI API key is not configured');
    }

    this.openai = new OpenAI({ apiKey: openaiConfig.apiKey });
  }

  async proxyToOpenAI(
    payload: ChatCompletionCreateParamsNonStreaming,
  ): Promise<OpenAiResponseDto> {
    this.logger.log('Proxying request to OpenAI', 'OpenAiProxyService');

    try {
      const completion = await this.openai.chat.completions.create(payload);

      this.logger.log(
        'Successfully received response from OpenAI',
        'OpenAiProxyService',
      );

      return completion as OpenAiResponseDto;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to proxy request to OpenAI: ${errorMessage}`,
        errorStack,
        'OpenAiProxyService',
      );

      throw new ExternalServiceException(
        `OpenAI API request failed: ${errorMessage}`,
        'OpenAI',
      );
    }
  }
}
